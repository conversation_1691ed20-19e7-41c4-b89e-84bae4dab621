#!/bin/bash

# GFW风控检测脚本
# 用于检测服务器IP是否被GFW封锁或干扰

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# 辅助函数
info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

print_header() {
    echo -e "${CYAN}${BOLD}================================================${NC}"
    echo -e "${CYAN}${BOLD}           GFW风控检测工具${NC}"
    echo -e "${CYAN}${BOLD}================================================${NC}"
    echo ""
}

# 主检测函数
test_gfw_blocking() {
    local target_ip="$1"
    local test_ports=("443" "8443" "80" "22")
    
    if [ -z "$target_ip" ]; then
        read -p "请输入要测试的服务器IP地址: " target_ip
    fi
    
    if [ -z "$target_ip" ]; then
        error "未提供测试IP地址"
        return 1
    fi
    
    info "开始对 ${target_ip} 进行GFW风控检测..."
    echo "测试时间: $(date)"
    echo "================================================"
    
    # 1. 基础网络层测试
    echo -e "${YELLOW}[1/5] 基础网络层测试 (ICMP)${NC}"
    echo "命令: ping -c 4 ${target_ip}"
    if ping -c 4 "$target_ip" >/dev/null 2>&1; then
        success "✓ ICMP ping 正常 - 基础网络连通"
        ping_result="正常"
    else
        error "✗ ICMP ping 失败 - 可能存在网络层封锁"
        ping_result="失败"
    fi
    echo ""
    
    # 2. 传输层端口测试
    echo -e "${YELLOW}[2/5] 传输层端口连通性测试${NC}"
    local port_results=()
    for port in "${test_ports[@]}"; do
        echo -n "测试端口 ${port}: "
        if command -v nc >/dev/null 2>&1; then
            if timeout 5 nc -z "$target_ip" "$port" 2>/dev/null; then
                success "可达"
                port_results+=("${port}:可达")
            else
                warn "不可达"
                port_results+=("${port}:不可达")
            fi
        else
            warn "nc命令未找到，跳过端口测试"
            port_results+=("${port}:无法测试")
        fi
    done
    echo ""
    
    # 3. HTTP/HTTPS协议测试
    echo -e "${YELLOW}[3/5] HTTP/HTTPS协议测试${NC}"
    echo -n "HTTP连接测试 (端口80): "
    if timeout 10 curl -m 10 -s -o /dev/null -w "%{http_code}" "http://${target_ip}" 2>/dev/null | grep -q "200\|301\|302\|403\|404"; then
        success "正常"
        http_result="正常"
    else
        warn "异常"
        http_result="异常"
    fi
    
    echo -n "HTTPS连接测试 (端口443): "
    if timeout 10 curl -m 10 -s -k -o /dev/null -w "%{http_code}" "https://${target_ip}" 2>/dev/null | grep -q "200\|301\|302\|403\|404"; then
        success "正常"
        https_result="正常"
    else
        warn "异常"
        https_result="异常"
    fi
    echo ""
    
    # 4. 路由跟踪测试
    echo -e "${YELLOW}[4/5] 路由跟踪测试${NC}"
    if command -v traceroute >/dev/null 2>&1; then
        echo "路由路径 (前10跳):"
        timeout 30 traceroute -m 10 "$target_ip" 2>/dev/null | head -10
    else
        warn "traceroute 命令未找到，跳过路由测试"
    fi
    echo ""
    
    # 5. 代理协议专项测试
    echo -e "${YELLOW}[5/6] 代理协议专项测试${NC}"
    echo "注意: 简单端口测试可能无法反映代理协议的真实可用性"
    echo ""

    echo -n "TLS握手测试 (端口443): "
    if timeout 10 openssl s_client -connect "$target_ip:443" -servername www.tesla.com </dev/null 2>/dev/null | grep -q "CONNECTED"; then
        success "TLS握手成功"
        tls_result="成功"
    else
        warn "TLS握手失败"
        tls_result="失败"
    fi

    echo -n "QUIC连接测试 (端口8443): "
    if command -v curl >/dev/null 2>&1; then
        if timeout 10 curl -s --http3-only --connect-timeout 5 "https://$target_ip:8443" 2>/dev/null >/dev/null; then
            success "QUIC连接成功"
            quic_result="成功"
        else
            warn "QUIC连接失败"
            quic_result="失败"
        fi
    else
        warn "curl不支持HTTP/3，跳过QUIC测试"
        quic_result="无法测试"
    fi
    echo ""

    # 6. 综合分析报告
    echo -e "${YELLOW}[6/6] 综合分析报告${NC}"
    echo "================================================"
    echo -e "${CYAN}${BOLD}检测结果汇总:${NC}"
    echo "• 目标IP: ${target_ip}"
    echo "• ICMP Ping: ${ping_result}"
    echo "• HTTP协议: ${http_result}"
    echo "• HTTPS协议: ${https_result}"
    echo "• TLS握手: ${tls_result}"
    echo "• QUIC连接: ${quic_result}"
    echo ""
    
    echo -e "${CYAN}${BOLD}端口连通性:${NC}"
    for result in "${port_results[@]}"; do
        echo "• 端口 ${result}"
    done
    echo ""
    
    # 风控判断逻辑
    echo -e "${CYAN}${BOLD}GFW风控分析:${NC}"

    if [[ "$ping_result" == "失败" ]]; then
        error "⚠️  可能存在IP级别的完全封锁"
        echo "   建议: 更换服务器IP地址"
    elif [[ "${port_results[*]}" =~ "443:不可达" ]] && [[ "${port_results[*]}" =~ "8443:不可达" ]]; then
        if [[ "$tls_result" == "成功" ]] || [[ "$quic_result" == "成功" ]]; then
            warn "⚠️  端口扫描被阻断，但协议握手正常"
            echo "   分析: 这是GFW的反扫描机制，代理服务可能仍然可用"
            echo "   建议: 检查客户端实际连接状态，可能无需处理"
        else
            error "⚠️  代理端口和协议都被封锁"
            echo "   建议: 更换端口或使用端口转发"
        fi
    elif [[ "$https_result" == "异常" ]] && [[ "$http_result" == "正常" ]]; then
        warn "⚠️  HTTPS协议可能被干扰"
        echo "   建议: 检查TLS配置或使用其他协议"
    else
        success "✓ 未发现明显的GFW封锁迹象"
        echo "   如仍有连接问题，可能是:"
        echo "   1. 间歇性干扰 (建议多时段测试)"
        echo "   2. 协议特征检测 (建议优化配置)"
        echo "   3. 服务器端问题 (检查服务状态)"
    fi
    
    echo ""
    echo -e "${GREEN}${BOLD}进一步诊断建议:${NC}"
    echo "1. 在VPN客户端连接异常时立即运行此脚本对比结果"
    echo "2. 使用不同网络环境(如手机热点)重复测试"
    echo "3. 检查客户端实际连接状态和延迟测试"
    echo "4. 检查服务器端日志: journalctl -u sing-box -f"
    echo "5. 监控客户端连接日志，观察是否有握手失败"
    echo "6. 如果客户端仍可正常使用，可能只是反扫描机制"
    echo "7. 考虑使用CDN或中转服务作为长期方案"
    echo ""
    echo -e "${BLUE}${BOLD}重要提醒:${NC}"
    echo "• 简单的端口扫描测试可能触发GFW的反扫描机制"
    echo "• 如果VPN客户端仍能正常工作，说明代理协议握手正常"
    echo "• 建议重点关注客户端的实际使用体验而非端口测试结果"
    echo "================================================"
}

# 脚本入口
print_header

# 检查必要工具
missing_tools=()
for tool in "ping" "curl"; do
    if ! command -v "$tool" >/dev/null 2>&1; then
        missing_tools+=("$tool")
    fi
done

if [ ${#missing_tools[@]} -gt 0 ]; then
    warn "缺少必要工具: ${missing_tools[*]}"
    echo "请安装后重新运行脚本"
    exit 1
fi

# 执行检测
if [ $# -eq 0 ]; then
    test_gfw_blocking
else
    test_gfw_blocking "$1"
fi
