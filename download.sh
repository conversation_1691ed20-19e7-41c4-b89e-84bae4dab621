#!/bin/bash

# =============================================================================
# 多线程断点下载客户端 - 一体化版本
# Multi-threaded Download Client for VPS Proxy - All-in-One Version
# =============================================================================
# Author: AI Assistant
# Version: 3.0
# Description: 集成所有功能的增强版下载客户端
# Features: 交互式配置、多种网络模式、断点续传、批量下载、配置管理
# Usage: ./download_client.sh [options] [download_url]
# =============================================================================

set -e

# =============================================================================
# 默认配置 / Default Configuration
# =============================================================================
DEFAULT_THREADS=8
PROXY_PORT=8080
CHUNK_SIZE="10M"
NETWORK_MODE="auto"
MAX_RETRIES=5
RETRY_DELAY=3
ENABLE_RESUME=true
VERBOSE_MODE=false

# =============================================================================
# 颜色输出 / Color Output
# =============================================================================
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# =============================================================================
# 输出函数 / Output Functions
# =============================================================================
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}=== $1 ===${NC}"
}

# =============================================================================
# 内置配置管理 / Built-in Configuration Management
# =============================================================================

# 稳定网络配置
setup_stable_network() {
    export NETWORK_MODE="stable"
    export DEFAULT_THREADS="12"
    export CHUNK_SIZE="20M"
    export MAX_RETRIES="3"
    export RETRY_DELAY="2"
    print_info "已切换到稳定网络配置"
}

# 不稳定网络配置
setup_unstable_network() {
    export NETWORK_MODE="unstable"
    export DEFAULT_THREADS="6"
    export CHUNK_SIZE="5M"
    export MAX_RETRIES="10"
    export RETRY_DELAY="5"
    print_info "已切换到不稳定网络配置"
}

# 大文件下载配置
setup_large_file() {
    export DEFAULT_THREADS="16"
    export CHUNK_SIZE="50M"
    export MAX_RETRIES="8"
    export RETRY_DELAY="3"
    export ENABLE_RESUME="true"
    print_info "已切换到大文件下载配置"
}

# 移动网络配置
setup_mobile_network() {
    export NETWORK_MODE="unstable"
    export DEFAULT_THREADS="4"
    export CHUNK_SIZE="2M"
    export MAX_RETRIES="15"
    export RETRY_DELAY="8"
    print_info "已切换到移动网络配置"
}

# 显示当前配置
show_current_config() {
    print_header "当前配置"
    echo "  VPS服务器: ${PROXY_SERVER:-未设置}:${PROXY_PORT:-8080}"
    echo "  网络模式: ${NETWORK_MODE:-auto}"
    echo "  下载线程: ${DEFAULT_THREADS:-8}"
    echo "  分片大小: ${CHUNK_SIZE:-10M}"
    echo "  最大重试: ${MAX_RETRIES:-5}"
    echo "  重试延迟: ${RETRY_DELAY:-3}秒"
    echo "  断点续传: ${ENABLE_RESUME:-true}"
    echo "  详细模式: ${VERBOSE_MODE:-false}"
}

# 配置向导
config_wizard() {
    print_header "配置向导"
    echo "选择预设配置："
    echo "  1) 稳定网络 (高速宽带)"
    echo "  2) 不稳定网络 (移动网络/高延迟)"
    echo "  3) 大文件下载 (>10GB)"
    echo "  4) 移动网络 (4G/5G)"
    echo "  5) 自定义配置"
    echo "  6) 显示当前配置"
    echo ""
    read -p "请选择 [1-6]: " config_choice
    
    case "$config_choice" in
        1) setup_stable_network ;;
        2) setup_unstable_network ;;
        3) setup_large_file ;;
        4) setup_mobile_network ;;
        5) 
            print_info "自定义配置模式"
            read -p "下载线程数 [4-32, 默认: $DEFAULT_THREADS]: " custom_threads
            if [[ "$custom_threads" =~ ^[0-9]+$ ]] && [ "$custom_threads" -ge 4 ] && [ "$custom_threads" -le 32 ]; then
                export DEFAULT_THREADS="$custom_threads"
            fi
            
            read -p "分片大小 [1M-100M, 默认: $CHUNK_SIZE]: " custom_chunk
            if [[ "$custom_chunk" =~ ^[0-9]+[MmGg]?$ ]]; then
                export CHUNK_SIZE="$custom_chunk"
            fi
            
            read -p "最大重试次数 [1-20, 默认: $MAX_RETRIES]: " custom_retries
            if [[ "$custom_retries" =~ ^[0-9]+$ ]] && [ "$custom_retries" -ge 1 ] && [ "$custom_retries" -le 20 ]; then
                export MAX_RETRIES="$custom_retries"
            fi
            ;;
        6) show_current_config ;;
        *) print_warning "无效选择，使用默认配置" ;;
    esac
}

# =============================================================================
# 工具函数 / Utility Functions
# =============================================================================

# 获取当前公网IP
get_current_ip() {
    local ip=""
    ip=$(curl -s --connect-timeout 5 ifconfig.me 2>/dev/null) || \
    ip=$(curl -s --connect-timeout 5 ipinfo.io/ip 2>/dev/null) || \
    ip=$(curl -s --connect-timeout 5 icanhazip.com 2>/dev/null) || \
    ip="unknown"
    echo "$ip"
}

# 检查依赖工具
check_dependencies() {
    local missing_tools=()
    
    if ! command -v curl >/dev/null 2>&1; then
        missing_tools+=("curl")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "缺少必需工具: ${missing_tools[*]}"
        print_info "请安装缺少的工具："
        for tool in "${missing_tools[@]}"; do
            case "$tool" in
                "curl")
                    echo "  Ubuntu/Debian: sudo apt-get install curl"
                    echo "  CentOS/RHEL: sudo yum install curl"
                    echo "  Alpine: apk add curl"
                    ;;
            esac
        done
        exit 1
    fi
    
    # 检查aria2c（可选）
    if command -v aria2c >/dev/null 2>&1; then
        ARIA2_AVAILABLE=true
        print_info "检测到aria2c，将使用多线程下载"
    else
        ARIA2_AVAILABLE=false
        print_warning "未检测到aria2c，将使用curl单线程下载"
        print_info "安装aria2c以获得更好的下载性能："
        echo "  Ubuntu/Debian: sudo apt-get install aria2"
        echo "  CentOS/RHEL: sudo yum install aria2"
        echo "  Alpine: apk add aria2"
    fi
}

# 格式化文件大小
format_size() {
    local size=$1
    if [ -z "$size" ] || [ "$size" = "0" ]; then
        echo "Unknown"
        return
    fi
    
    if [ "$size" -lt 1024 ]; then
        echo "${size}B"
    elif [ "$size" -lt 1048576 ]; then
        echo "$((size / 1024))KB"
    elif [ "$size" -lt ********** ]; then
        echo "$((size / 1048576))MB"
    else
        echo "$((size / **********))GB"
    fi
}

# 测试代理服务器连通性
test_proxy_connectivity() {
    local proxy_server="$1"
    local proxy_port="${2:-$PROXY_PORT}"
    
    print_info "测试代理服务器连通性..."
    
    # 测试基本连接
    if ! curl -s --connect-timeout 10 "http://${proxy_server}:${proxy_port}/health" >/dev/null 2>&1; then
        print_warning "无法连接到代理服务器健康检查端点"
        print_info "尝试测试基本连接..."
        
        if ! curl -s --connect-timeout 10 "http://${proxy_server}:${proxy_port}/" >/dev/null 2>&1; then
            print_error "无法连接到代理服务器 ${proxy_server}:${proxy_port}"
            print_info "请检查："
            echo "  1. VPS服务器是否正在运行"
            echo "  2. 代理服务是否已启动"
            echo "  3. 防火墙是否允许端口 ${proxy_port}"
            echo "  4. 网络连接是否正常"
            return 1
        fi
    fi
    
    print_success "代理服务器连接正常"
    return 0
}

# 获取文件信息
get_file_info() {
    local url="$1"
    local proxy_url="$2"
    
    print_info "获取文件信息..."
    
    # 使用HEAD请求获取文件大小
    local headers=$(curl -sI --connect-timeout 30 "$proxy_url" 2>/dev/null)
    local content_length=$(echo "$headers" | grep -i "content-length" | cut -d: -f2 | tr -d ' \r\n')
    local accept_ranges=$(echo "$headers" | grep -i "accept-ranges" | cut -d: -f2 | tr -d ' \r\n')
    
    if [ -n "$content_length" ] && [ "$content_length" -gt 0 ]; then
        FILE_SIZE="$content_length"
        print_info "文件大小: $(format_size $FILE_SIZE)"
    else
        FILE_SIZE="unknown"
        print_warning "无法获取文件大小"
    fi
    
    if [ "$accept_ranges" = "bytes" ]; then
        SUPPORTS_RANGE=true
        print_info "支持断点续传"
    else
        SUPPORTS_RANGE=false
        print_warning "不支持断点续传"
    fi
}

# =============================================================================
# 交互式配置 / Interactive Configuration
# =============================================================================

# 交互式配置函数
interactive_config() {
    local current_ip=$(get_current_ip)

    print_header "交互式下载配置"
    echo ""

    # VPS IP configuration
    print_info "1. VPS 服务器配置"
    if [ "$current_ip" != "unknown" ]; then
        echo "检测到当前机器公网IP: $current_ip"
        read -p "是否使用当前机器作为VPS代理服务器? [Y/n]: " use_current_ip
        if [[ "$use_current_ip" =~ ^[Nn]$ ]]; then
            read -p "请输入VPS服务器IP地址: " proxy_server
        else
            proxy_server="$current_ip"
        fi
    else
        read -p "请输入VPS服务器IP地址: " proxy_server
    fi

    # Port configuration
    read -p "VPS代理服务端口 [默认: $PROXY_PORT]: " custom_port
    if [ -n "$custom_port" ]; then
        PROXY_PORT="$custom_port"
    fi

    # Download URL
    echo ""
    print_info "2. 下载文件配置"
    read -p "请输入下载文件的URL: " download_url

    # Output filename
    local default_filename=$(basename "$download_url")
    if [ -z "$default_filename" ] || [ "$default_filename" = "/" ]; then
        default_filename="downloaded_file"
    fi
    read -p "输出文件名 [默认: $default_filename]: " output_file
    if [ -z "$output_file" ]; then
        output_file="$default_filename"
    fi

    # Thread configuration
    echo ""
    print_info "3. 下载线程配置"
    echo "推荐线程数："
    echo "  - 小文件(<100MB): 4-6线程"
    echo "  - 中等文件(100MB-10GB): 8-12线程"
    echo "  - 大文件(>10GB): 12-16线程"
    read -p "请输入下载线程数 [6-32, 默认: $DEFAULT_THREADS]: " threads
    if [ -z "$threads" ]; then
        threads="$DEFAULT_THREADS"
    elif ! [[ "$threads" =~ ^[0-9]+$ ]] || [ "$threads" -lt 6 ] || [ "$threads" -gt 32 ]; then
        print_warning "线程数无效，使用默认值: $DEFAULT_THREADS"
        threads="$DEFAULT_THREADS"
    fi

    # Network optimization
    echo ""
    print_info "4. 网络优化配置"
    echo "网络环境选择："
    echo "  1) 稳定网络 (高速宽带)"
    echo "  2) 不稳定网络 (移动网络/高延迟)"
    echo "  3) 自动检测"
    read -p "请选择网络环境 [1-3, 默认: 3]: " network_choice

    case "$network_choice" in
        1)
            NETWORK_MODE="stable"
            CHUNK_SIZE="20M"
            MAX_RETRIES=3
            RETRY_DELAY=2
            ;;
        2)
            NETWORK_MODE="unstable"
            CHUNK_SIZE="5M"
            MAX_RETRIES=10
            RETRY_DELAY=5
            threads=$((threads / 2))  # 减少线程数
            if [ "$threads" -lt 4 ]; then
                threads=4
            fi
            ;;
        *)
            NETWORK_MODE="auto"
            ;;
    esac

    # Resume support
    echo ""
    print_info "5. 断点续传配置"
    read -p "是否启用断点续传? [Y/n]: " enable_resume
    if [[ "$enable_resume" =~ ^[Nn]$ ]]; then
        ENABLE_RESUME=false
    else
        ENABLE_RESUME=true
    fi

    # Advanced options
    echo ""
    print_info "6. 高级选项"
    read -p "是否显示详细下载信息? [Y/n]: " verbose_mode
    if [[ "$verbose_mode" =~ ^[Nn]$ ]]; then
        VERBOSE_MODE=false
    else
        VERBOSE_MODE=true
    fi

    # Summary
    echo ""
    print_header "配置总结"
    echo "VPS服务器: $proxy_server:$PROXY_PORT"
    echo "下载URL: $download_url"
    echo "输出文件: $output_file"
    echo "下载线程: $threads"
    echo "网络模式: $NETWORK_MODE"
    echo "分片大小: $CHUNK_SIZE"
    echo "最大重试: $MAX_RETRIES 次"
    echo "重试延迟: $RETRY_DELAY 秒"
    echo "断点续传: $([ "$ENABLE_RESUME" = true ] && echo "启用" || echo "禁用")"
    echo "详细模式: $([ "$VERBOSE_MODE" = true ] && echo "启用" || echo "禁用")"
    echo ""

    read -p "确认开始下载? [Y/n]: " confirm_download
    if [[ "$confirm_download" =~ ^[Nn]$ ]]; then
        print_info "下载已取消"
        exit 0
    fi

    # Export configuration for main function
    export INTERACTIVE_PROXY_SERVER="$proxy_server"
    export INTERACTIVE_DOWNLOAD_URL="$download_url"
    export INTERACTIVE_THREADS="$threads"
    export INTERACTIVE_OUTPUT_FILE="$output_file"
}

# =============================================================================
# 下载函数 / Download Functions
# =============================================================================

# aria2c下载函数
download_with_aria2() {
    local proxy_url="$1"
    local output_file="$2"
    local threads="$3"

    print_info "使用aria2c开始多线程下载..."

    local aria2_args=(
        "--split=$threads"
        "--max-connection-per-server=$threads"
        "--min-split-size=$CHUNK_SIZE"
        "--max-tries=$MAX_RETRIES"
        "--retry-wait=$RETRY_DELAY"
        "--timeout=120"
        "--connect-timeout=30"
        "--summary-interval=5"
        "--out=$output_file"
    )

    # Add resume support if enabled
    if [ "${ENABLE_RESUME:-true}" = true ]; then
        aria2_args+=("--continue=true")
    fi

    # Add verbose mode if enabled
    if [ "${VERBOSE_MODE:-false}" = true ]; then
        aria2_args+=("--console-log-level=info")
    else
        aria2_args+=("--console-log-level=notice" "--download-result=hide")
    fi

    # Network mode specific optimizations
    case "${NETWORK_MODE:-auto}" in
        "unstable")
            aria2_args+=("--lowest-speed-limit=1K" "--max-resume-failure-tries=10")
            ;;
        "stable")
            aria2_args+=("--max-concurrent-downloads=1" "--split=$threads")
            ;;
    esac

    aria2c "${aria2_args[@]}" "$proxy_url"
}

# curl下载函数
download_with_curl() {
    local proxy_url="$1"
    local output_file="$2"

    print_info "使用curl开始下载..."

    local curl_args=(
        "-L"
        "--connect-timeout" "30"
        "--max-time" "0"
        "--retry" "$MAX_RETRIES"
        "--retry-delay" "$RETRY_DELAY"
        "-o" "$output_file"
    )

    # Add resume support if enabled
    if [ "${ENABLE_RESUME:-true}" = true ]; then
        curl_args+=("-C" "-")
    fi

    # Add progress display
    if [ "${VERBOSE_MODE:-false}" = true ]; then
        curl_args+=("--progress-bar")
    else
        curl_args+=("--silent" "--show-error" "--progress-bar")
    fi

    # Network mode specific optimizations
    case "${NETWORK_MODE:-auto}" in
        "unstable")
            curl_args+=("--retry-max-time" "600" "--speed-limit" "1024" "--speed-time" "30")
            ;;
        "stable")
            curl_args+=("--speed-limit" "10240" "--speed-time" "15")
            ;;
    esac

    curl "${curl_args[@]}" "$proxy_url"
}

# 主下载函数
perform_download() {
    local proxy_server="$1"
    local download_url="$2"
    local threads="$3"
    local output_file="$4"
    local proxy_port="${5:-$PROXY_PORT}"

    # 构建代理URL
    local encoded_url=$(printf '%s\n' "$download_url" | sed 's/[[\.*^$()+?{|]/\\&/g')
    local proxy_url="http://${proxy_server}:${proxy_port}/download?url=${download_url}"

    print_info "下载URL: $download_url"
    print_info "线程数: $threads"
    print_info "输出文件: $output_file"
    echo ""

    # 测试代理服务器连通性
    if ! test_proxy_connectivity "$proxy_server" "$proxy_port"; then
        exit 1
    fi

    # 获取文件信息
    get_file_info "$download_url" "$proxy_url"

    # 记录开始时间
    local start_time=$(date +%s)

    # 选择下载工具并开始下载
    if [ "$ARIA2_AVAILABLE" = true ] && [ "$threads" -gt 1 ]; then
        download_with_aria2 "$proxy_url" "$output_file" "$threads"
    else
        download_with_curl "$proxy_url" "$output_file"
    fi

    # 检查下载结果
    if [ $? -eq 0 ] && [ -f "$output_file" ]; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        local file_size=$(stat -c%s "$output_file" 2>/dev/null || echo "0")
        local speed="Unknown"

        if [ "$duration" -gt 0 ] && [ "$file_size" -gt 0 ]; then
            speed="$((file_size / duration / 1024))KB/s"
        fi

        print_success "下载完成!"
        print_info "文件: $output_file"
        print_info "大小: $(format_size $file_size)"
        print_info "用时: ${duration}s"
        print_info "平均速度: $speed"
    else
        print_error "下载失败"
        exit 1
    fi
}

# =============================================================================
# 帮助和使用说明 / Help and Usage
# =============================================================================

# 显示使用帮助
show_usage() {
    cat << 'EOF'
多线程断点下载客户端 - 一体化版本
Multi-threaded Download Client for VPS Proxy - All-in-One Version

用法 / Usage:
  ./download_client.sh [选项] [下载URL]

选项 / Options:
  -i, --interactive     交互式配置模式
  -c, --config         配置向导模式
  -h, --help           显示此帮助信息
  -v, --version        显示版本信息

非交互式用法 / Non-interactive Usage:
  ./download_client.sh <VPS_IP> <下载URL> [线程数] [输出文件]

示例 / Examples:
  # 交互式配置（推荐新用户）
  ./download_client.sh --interactive

  # 配置向导
  ./download_client.sh --config

  # 直接下载
  ./download_client.sh ************* https://example.com/file.zip

  # 指定线程数和输出文件
  ./download_client.sh ************* https://example.com/file.zip 16 my_file.zip

内置配置管理 / Built-in Configuration:
  在脚本中调用以下函数来快速配置：
  - setup_stable_network      # 稳定网络配置
  - setup_unstable_network    # 不稳定网络配置
  - setup_large_file          # 大文件下载配置
  - setup_mobile_network      # 移动网络配置
  - show_current_config       # 显示当前配置

环境变量 / Environment Variables:
  PROXY_PORT       代理服务端口 (默认: 8080)
  CHUNK_SIZE       分片大小 (默认: 10M)
  NETWORK_MODE     网络模式: auto/stable/unstable (默认: auto)
  MAX_RETRIES      最大重试次数 (默认: 5)
  RETRY_DELAY      重试延迟秒数 (默认: 3)
  ENABLE_RESUME    断点续传: true/false (默认: true)
  VERBOSE_MODE     详细模式: true/false (默认: false)

网络优化建议 / Network Optimization:
  稳定网络:   12-16线程, 20M分片, 3次重试
  不稳定网络: 4-6线程,  5M分片,  10次重试
  大文件:     16线程,   50M分片, 8次重试
  移动网络:   4线程,    2M分片,  15次重试

依赖工具 / Dependencies:
  必需: curl
  推荐: aria2c (用于多线程下载)

安装aria2c:
  Ubuntu/Debian: sudo apt-get install aria2
  CentOS/RHEL:   sudo yum install aria2
  Alpine:        apk add aria2

更多信息请访问项目文档。
EOF
}

# 显示版本信息
show_version() {
    echo "多线程断点下载客户端 v3.0"
    echo "Multi-threaded Download Client for VPS Proxy v3.0"
    echo "集成所有功能的一体化版本"
    echo ""
    echo "功能特性:"
    echo "  ✓ 交互式配置"
    echo "  ✓ 多种网络模式"
    echo "  ✓ 断点续传"
    echo "  ✓ 批量下载支持"
    echo "  ✓ 内置配置管理"
    echo "  ✓ 智能网络优化"
}

# =============================================================================
# 批量下载功能 / Batch Download Functions
# =============================================================================

# 批量下载函数
batch_download() {
    local proxy_server="$1"
    local url_file="$2"
    local threads="${3:-$DEFAULT_THREADS}"

    if [ ! -f "$url_file" ]; then
        print_error "URL文件不存在: $url_file"
        exit 1
    fi

    print_header "批量下载模式"
    print_info "VPS服务器: $proxy_server"
    print_info "URL文件: $url_file"
    print_info "线程数: $threads"
    echo ""

    local total_urls=$(wc -l < "$url_file")
    local current=0

    while IFS= read -r url; do
        # 跳过空行和注释行
        if [[ -z "$url" || "$url" =~ ^[[:space:]]*# ]]; then
            continue
        fi

        current=$((current + 1))
        local filename=$(basename "$url")
        if [ -z "$filename" ] || [ "$filename" = "/" ]; then
            filename="file_${current}"
        fi

        print_header "下载 $current/$total_urls: $filename"
        perform_download "$proxy_server" "$url" "$threads" "$filename"
        echo ""
    done < "$url_file"

    print_success "批量下载完成! 共处理 $current 个文件"
}

# =============================================================================
# 主函数 / Main Function
# =============================================================================

# 主函数
main() {
    # 检查依赖
    check_dependencies

    # 解析命令行参数
    case "${1:-}" in
        "--interactive"|"-i")
            interactive_config
            # 使用交互式配置
            local proxy_server="$INTERACTIVE_PROXY_SERVER"
            local download_url="$INTERACTIVE_DOWNLOAD_URL"
            local threads="$INTERACTIVE_THREADS"
            local output_file="$INTERACTIVE_OUTPUT_FILE"
            ;;
        "--config"|"-c")
            config_wizard
            show_current_config
            exit 0
            ;;
        "--help"|"-h")
            show_usage
            exit 0
            ;;
        "--version"|"-v")
            show_version
            exit 0
            ;;
        "--batch"|"-b")
            if [ $# -lt 3 ]; then
                print_error "批量下载模式需要指定VPS服务器和URL文件"
                echo "用法: $0 --batch <VPS_IP> <URL文件> [线程数]"
                exit 1
            fi
            batch_download "$2" "$3" "${4:-$DEFAULT_THREADS}"
            exit 0
            ;;
        "")
            # 无参数时显示交互式配置
            interactive_config
            local proxy_server="$INTERACTIVE_PROXY_SERVER"
            local download_url="$INTERACTIVE_DOWNLOAD_URL"
            local threads="$INTERACTIVE_THREADS"
            local output_file="$INTERACTIVE_OUTPUT_FILE"
            ;;
        *)
            # 命令行模式
            if [ $# -lt 2 ]; then
                print_error "参数不足"
                echo ""
                show_usage
                exit 1
            fi

            local proxy_server="$1"
            local download_url="$2"
            local threads="${3:-$DEFAULT_THREADS}"
            local output_file="$4"

            # 如果没有指定输出文件名，从URL提取
            if [ -z "$output_file" ]; then
                output_file=$(basename "$download_url")
                if [ -z "$output_file" ] || [ "$output_file" = "/" ]; then
                    output_file="downloaded_file"
                fi
            fi
            ;;
    esac

    # 验证参数
    if [ -z "$proxy_server" ] || [ -z "$download_url" ]; then
        print_error "缺少必要参数"
        exit 1
    fi

    # 验证线程数
    if ! [[ "$threads" =~ ^[0-9]+$ ]] || [ "$threads" -lt 1 ] || [ "$threads" -gt 32 ]; then
        print_warning "线程数无效，使用默认值: $DEFAULT_THREADS"
        threads="$DEFAULT_THREADS"
    fi

    # 开始下载
    perform_download "$proxy_server" "$download_url" "$threads" "$output_file"
}

# =============================================================================
# 脚本入口 / Script Entry Point
# =============================================================================

# 如果脚本被直接执行（而不是被source），则运行主函数
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
