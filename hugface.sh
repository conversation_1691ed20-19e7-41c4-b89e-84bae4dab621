#!/usr/bin/env bash
# HuggingFace 下载工具 - 交互式配置版本
# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

trap 'printf "${YELLOW}\nDownload interrupted. If you re-run the command, you can resume the download from the breakpoint.\n${NC}"; exit 1' INT

# 输出函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}=== $1 ===${NC}"
}

# 交互式配置函数
interactive_config() {
    print_header "HuggingFace 下载工具配置"
    echo ""

    # 1. 询问是否使用国内代理
    print_info "1. 代理设置"
    echo "是否使用国内镜像代理 (hf-mirror.com)？"
    echo "  [1] 是 - 使用国内镜像 (推荐中国大陆用户)"
    echo "  [2] 否 - 使用官方地址"
    echo ""
    while true; do
        read -p "请选择 [1-2]: " proxy_choice
        case $proxy_choice in
            1)
                export HF_ENDPOINT="https://hf-mirror.com"
                print_success "已设置使用国内镜像: $HF_ENDPOINT"
                break
                ;;
            2)
                export HF_ENDPOINT="https://huggingface.co"
                print_success "已设置使用官方地址: $HF_ENDPOINT"
                break
                ;;
            *)
                print_warning "请输入有效选项 (1 或 2)"
                ;;
        esac
    done
    echo ""

    # 2. 选择下载工具
    print_info "2. 下载工具设置"
    echo "请选择下载工具："
    echo "  [1] aria2c - 多线程下载 (推荐，速度快)"
    echo "  [2] wget - 单线程下载 (兼容性好)"
    echo ""
    while true; do
        read -p "请选择 [1-2]: " tool_choice
        case $tool_choice in
            1)
                TOOL="aria2c"
                print_success "已选择下载工具: aria2c"
                break
                ;;
            2)
                TOOL="wget"
                print_success "已选择下载工具: wget"
                break
                ;;
            *)
                print_warning "请输入有效选项 (1 或 2)"
                ;;
        esac
    done
    echo ""

    # 3. 设置线程数 (仅对aria2c有效)
    if [[ "$TOOL" == "aria2c" ]]; then
        print_info "3. 线程数设置"
        echo "请选择下载线程数 (4-16)："
        echo "  建议: 4-8线程适合大多数情况"
        echo "  注意: 线程数过多可能导致连接不稳定"
        echo ""
        while true; do
            read -p "请输入线程数 [4-16]: " threads_input
            if [[ "$threads_input" =~ ^[0-9]+$ ]] && [ "$threads_input" -ge 4 ] && [ "$threads_input" -le 16 ]; then
                THREADS="$threads_input"
                print_success "已设置线程数: $THREADS"
                break
            else
                print_warning "请输入4-16之间的数字"
            fi
        done
        echo ""
    else
        THREADS=1
        print_info "3. wget使用单线程下载"
        echo ""
    fi

    # 4. 用户认证信息设置
    print_info "4. 用户认证设置"
    echo "默认认证信息:"
    echo "  用户名: ruyiding"
    echo "  Token: *************************************"
    echo ""
    echo "是否使用默认认证信息？"
    echo "  [1] 是 - 使用默认认证"
    echo "  [2] 否 - 自定义认证信息"
    echo "  [3] 跳过 - 不使用认证 (仅限公开模型)"
    echo ""
    while true; do
        read -p "请选择 [1-3]: " auth_choice
        case $auth_choice in
            1)
                HF_USERNAME="ruyiding"
                HF_TOKEN="*************************************"
                print_success "已设置默认认证信息"
                break
                ;;
            2)
                echo ""
                read -p "请输入HuggingFace用户名: " custom_username
                read -p "请输入HuggingFace Token: " custom_token
                if [[ -n "$custom_username" && -n "$custom_token" ]]; then
                    HF_USERNAME="$custom_username"
                    HF_TOKEN="$custom_token"
                    print_success "已设置自定义认证信息"
                else
                    print_warning "用户名或Token不能为空，使用默认认证"
                    HF_USERNAME="ruyiding"
                    HF_TOKEN="*************************************"
                fi
                break
                ;;
            3)
                HF_USERNAME=""
                HF_TOKEN=""
                print_warning "已跳过认证设置，仅能下载公开模型"
                break
                ;;
            *)
                print_warning "请输入有效选项 (1, 2 或 3)"
                ;;
        esac
    done
    echo ""

    # 显示配置摘要
    print_header "配置摘要"
    echo "代理地址: $HF_ENDPOINT"
    echo "下载工具: $TOOL"
    echo "线程数: $THREADS"
    if [[ -n "$HF_USERNAME" ]]; then
        echo "用户名: $HF_USERNAME"
        echo "Token: ${HF_TOKEN:0:10}..."
    else
        echo "认证: 未设置"
    fi
    echo ""

    read -p "确认以上配置并开始下载？ [y/N]: " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        print_info "配置已取消"
        exit 0
    fi
    echo ""
}

display_help() {
    cat << EOF
HuggingFace 下载工具 - 交互式配置版本

用法:
  $0                                              # 交互式模式 (询问模型ID)
  $0 <repo_id>                                    # 交互式配置模式
  $0 <repo_id> [选项...]                          # 命令行模式

描述:
  从 Hugging Face 下载模型或数据集，支持交互式配置和命令行参数

交互式模式:
  - 无参数运行时，会先询问要下载的模型仓库ID
  - 只提供 repo_id 时，会启动交互式配置向导，引导您设置：
    * 代理设置 (国内镜像 vs 官方地址)
    * 下载工具 (aria2c vs wget)
    * 线程数设置 (4-16)
    * 用户认证信息

命令行参数:
  repo_id         Hugging Face 仓库ID，格式: 'org/repo_name'
  --include       指定要包含的文件模式，支持多个模式
  --exclude       指定要排除的文件模式，支持多个模式
  --hf_username   Hugging Face 用户名 (非邮箱)
  --hf_token      Hugging Face 访问令牌
  --tool          下载工具: aria2c (默认) 或 wget
  -x              aria2c 下载线程数 (默认: 4)
  --dataset       标记为数据集下载
  --local-dir     本地存储目录路径

环境变量:
  HF_ENDPOINT     设置 Hugging Face 端点 (默认: https://huggingface.co)
                  国内镜像: https://hf-mirror.com

示例:
  # 交互式模式 (推荐)
  $0                                              # 会询问模型ID并配置
  $0 bigscience/bloom-560m                        # 直接配置指定模型

  # 命令行模式 (高级用户)
  $0 bigscience/bloom-560m --exclude *.safetensors --tool aria2c -x 8
  $0 meta-llama/Llama-2-7b --hf_username myuser --hf_token mytoken
  $0 lavita/medical-qa-shared-task-v1-toy --dataset

  # 使用国内镜像
  HF_ENDPOINT=https://hf-mirror.com $0 bigscience/bloom-560m

默认认证信息:
  用户名: ruyiding
  Token: *************************************
EOF
    exit 1
}

# 检查帮助选项
if [[ $# -gt 0 ]] && ([[ "$1" =~ ^-h ]] || [[ "$1" == "--help" ]]); then
    display_help
fi

# 如果没有提供模型ID或者提供了模型ID但没有其他参数，启动交互式配置
if [[ $# -eq 0 ]]; then
    # 没有任何参数，询问模型ID并启动交互式配置
    print_header "HuggingFace 模型下载工具"
    echo ""
    print_info "请输入要下载的模型仓库ID"
    echo "格式: org/repo_name"
    echo "示例: bigscience/bloom-560m, meta-llama/Llama-2-7b, microsoft/DialoGPT-medium"
    echo ""

    while true; do
        read -p "模型仓库ID: " MODEL_ID
        if [[ -n "$MODEL_ID" && "$MODEL_ID" =~ ^[a-zA-Z0-9_.-]+/[a-zA-Z0-9_.-]+$ ]]; then
            print_success "已选择模型: $MODEL_ID"
            break
        else
            print_warning "请输入有效的模型仓库ID (格式: org/repo_name)"
        fi
    done
    echo ""

    # 启动交互式配置
    interactive_config
elif [[ $# -eq 1 ]]; then
    # 只提供了模型ID，启动交互式配置
    MODEL_ID=$1
    print_header "启动交互式配置模式"
    echo "模型ID: $MODEL_ID"
    echo ""
    interactive_config
else
    # 提供了模型ID和其他参数，使用命令行模式
    MODEL_ID=$1
    shift
fi

# Default values (如果没有通过交互式配置设置)
TOOL=${TOOL:-"aria2c"}
THREADS=${THREADS:-4}
HF_ENDPOINT=${HF_ENDPOINT:-"https://huggingface.co"}

INCLUDE_PATTERNS=()
EXCLUDE_PATTERNS=()

while [[ $# -gt 0 ]]; do
    case $1 in
        --include)
            shift
            while [[ $# -gt 0 && ! $1 =~ ^-- ]]; do
                INCLUDE_PATTERNS+=("$1")
                shift
            done
            ;;
        --exclude)
            shift
            while [[ $# -gt 0 && ! $1 =~ ^-- ]]; do
                EXCLUDE_PATTERNS+=("$1")
                shift
            done
            ;;
        --hf_username) HF_USERNAME="$2"; shift 2 ;;
        --hf_token) HF_TOKEN="$2"; shift 2 ;;
        --tool) TOOL="$2"; shift 2 ;;
        -x) THREADS="$2"; shift 2 ;;
        --dataset) DATASET=1; shift ;;
        --local-dir) LOCAL_DIR="$2"; shift 2 ;;
        *) shift ;;
    esac
done

# Check if aria2, wget, curl, git, and git-lfs are installed
check_command() {
    if ! command -v $1 &>/dev/null; then
        echo -e "${RED}$1 is not installed. Please install it first.${NC}"
        exit 1
    fi
}

# Mark current repo safe when using shared file system like samba or nfs
ensure_ownership() {
    if git status 2>&1 | grep "fatal: detected dubious ownership in repository at" > /dev/null; then
        git config --global --add safe.directory "${PWD}"
        printf "${YELLOW}Detected dubious ownership in repository, mark ${PWD} safe using git, edit ~/.gitconfig if you want to reverse this.\n${NC}" 
    fi
}

[[ "$TOOL" == "aria2c" ]] && check_command aria2c
[[ "$TOOL" == "wget" ]] && check_command wget
check_command curl; check_command git; check_command git-lfs

if [[ -z "$LOCAL_DIR" ]]; then
    LOCAL_DIR="${MODEL_ID#*/}"
fi

if [[ "$DATASET" == 1 ]]; then
    MODEL_ID="datasets/$MODEL_ID"
fi
echo "Downloading to $LOCAL_DIR"

if [ -d "$LOCAL_DIR/.git" ]; then
    printf "${YELLOW}%s exists, Skip Clone.\n${NC}" "$LOCAL_DIR"
    cd "$LOCAL_DIR" && ensure_ownership && GIT_LFS_SKIP_SMUDGE=1 git pull || { printf "${RED}Git pull failed.${NC}\n"; exit 1; }
else
    REPO_URL="$HF_ENDPOINT/$MODEL_ID"
    GIT_REFS_URL="${REPO_URL}/info/refs?service=git-upload-pack"
    echo "Testing GIT_REFS_URL: $GIT_REFS_URL"
    response=$(curl -s -o /dev/null -w "%{http_code}" "$GIT_REFS_URL")
    if [ "$response" == "401" ] || [ "$response" == "403" ]; then
        if [[ -z "$HF_USERNAME" || -z "$HF_TOKEN" ]]; then
            printf "${RED}HTTP Status Code: $response.\nThe repository requires authentication, but --hf_username and --hf_token is not passed. Please get token from https://huggingface.co/settings/tokens.\nExiting.\n${NC}"
            exit 1
        fi
        REPO_URL="https://$HF_USERNAME:$HF_TOKEN@${HF_ENDPOINT#https://}/$MODEL_ID"
    elif [ "$response" != "200" ]; then
        printf "${RED}Unexpected HTTP Status Code: $response\n${NC}"
        printf "${YELLOW}Executing debug command: curl -v %s\nOutput:${NC}\n" "$GIT_REFS_URL"
        curl -v "$GIT_REFS_URL"; printf "\n${RED}Git clone failed.\n${NC}"; exit 1
    fi
    echo "GIT_LFS_SKIP_SMUDGE=1 git clone $REPO_URL $LOCAL_DIR"

    GIT_LFS_SKIP_SMUDGE=1 git clone $REPO_URL $LOCAL_DIR && cd "$LOCAL_DIR" || { printf "${RED}Git clone failed.\n${NC}"; exit 1; }

    ensure_ownership

    while IFS= read -r file; do
        truncate -s 0 "$file"
    done <<< $(git lfs ls-files | cut -d ' ' -f 3-)
fi

printf "\nStart Downloading lfs files, bash script:\ncd $LOCAL_DIR\n"
files=$(git lfs ls-files | cut -d ' ' -f 3-)
declare -a urls

file_matches_include_patterns() {
    local file="$1"
    for pattern in "${INCLUDE_PATTERNS[@]}"; do
        if [[ "$file" == $pattern ]]; then
            return 0
        fi
    done
    return 1
}

file_matches_exclude_patterns() {
    local file="$1"
    for pattern in "${EXCLUDE_PATTERNS[@]}"; do
        if [[ "$file" == $pattern ]]; then
            return 0
        fi
    done
    return 1
}

while IFS= read -r file; do
    url="$HF_ENDPOINT/$MODEL_ID/resolve/main/$file"
    file_dir=$(dirname "$file")
    mkdir -p "$file_dir"
    if [[ "$TOOL" == "wget" ]]; then
        download_cmd="wget -c \"$url\" -O \"$file\""
        [[ -n "$HF_TOKEN" ]] && download_cmd="wget --header=\"Authorization: Bearer ${HF_TOKEN}\" -c \"$url\" -O \"$file\""
    else
        download_cmd="aria2c --console-log-level=error --file-allocation=none -x $THREADS -s $THREADS -k 1M -c \"$url\" -d \"$file_dir\" -o \"$(basename "$file")\""
        [[ -n "$HF_TOKEN" ]] && download_cmd="aria2c --header=\"Authorization: Bearer ${HF_TOKEN}\" --console-log-level=error --file-allocation=none -x $THREADS -s $THREADS -k 1M -c \"$url\" -d \"$file_dir\" -o \"$(basename "$file")\""
    fi

    if [[ ${#INCLUDE_PATTERNS[@]} -gt 0 ]]; then
        file_matches_include_patterns "$file" || { printf "# %s\n" "$download_cmd"; continue; }
    fi

    if [[ ${#EXCLUDE_PATTERNS[@]} -gt 0 ]]; then
        file_matches_exclude_patterns "$file" && { printf "# %s\n" "$download_cmd"; continue; }
    fi

    printf "%s\n" "$download_cmd"
    urls+=("$url|$file")
done <<< "$files"

for url_file in "${urls[@]}"; do
    IFS='|' read -r url file <<< "$url_file"
    printf "${YELLOW}Start downloading ${file}.\n${NC}" 
    file_dir=$(dirname "$file")
    if [[ "$TOOL" == "wget" ]]; then
        [[ -n "$HF_TOKEN" ]] && wget --header="Authorization: Bearer ${HF_TOKEN}" -c "$url" -O "$file" || wget -c "$url" -O "$file"
    else
        [[ -n "$HF_TOKEN" ]] && aria2c --header="Authorization: Bearer ${HF_TOKEN}" --console-log-level=error --file-allocation=none -x $THREADS -s $THREADS -k 1M -c "$url" -d "$file_dir" -o "$(basename "$file")" || aria2c --console-log-level=error --file-allocation=none -x $THREADS -s $THREADS -k 1M -c "$url" -d "$file_dir" -o "$(basename "$file")"
    fi
    [[ $? -eq 0 ]] && printf "Downloaded %s successfully.\n" "$url" || { printf "${RED}Failed to download %s.\n${NC}" "$url"; exit 1; }
done

printf "${GREEN}Download completed successfully.\n${NC}"