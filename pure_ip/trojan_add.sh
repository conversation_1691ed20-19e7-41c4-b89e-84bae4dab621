#!/bin/bash

# Trojan Add-on Script for existing Sing-Box Hysteria2 & Reality setup
# This script adds Trojan protocol to existing lvhy.sh installation

# --- Author Information ---
AUTHOR_NAME="Enhanced by AI Assistant"
ORIGINAL_AUTHOR="jcnf-那坨"
WEBSITE_URL="https://ybfl.net"

# --- Configuration (matching lvhy.sh) ---
SINGBOX_INSTALL_PATH_EXPECTED="/usr/local/bin/sing-box"
SINGBOX_CONFIG_DIR="/usr/local/etc/sing-box"
SINGBOX_CONFIG_FILE="${SINGBOX_CONFIG_DIR}/config.json"
SINGBOX_SERVICE_FILE="/etc/systemd/system/sing-box.service"

# Trojan specific configuration
TROJAN_CERT_DIR="/etc/trojan"
TROJAN_CERT_KEY="${TROJAN_CERT_DIR}/private.key"
TROJAN_CERT_PEM="${TROJAN_CERT_DIR}/cert.pem"
DEFAULT_TROJAN_PORT="8028"
DEFAULT_TROJAN_DOMAIN="www.cloudflare.com"

# Persistent info file (same as lvhy.sh)
PERSISTENT_INFO_FILE="${SINGBOX_CONFIG_DIR}/.last_singbox_script_info"

# Global variables
SINGBOX_CMD=""
TROJAN_PASSWORD=""
TROJAN_PORT=""
TROJAN_DOMAIN=""

# --- Colors (matching lvhy.sh) ---
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
UNDERLINE='\033[4m'
NC='\033[0m'

# --- Helper functions ---
info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

print_header() {
    echo -e "${MAGENTA}${BOLD}================================================${NC}"
    echo -e "${CYAN}${BOLD} Trojan 协议管理脚本 ${NC}"
    echo -e "${MAGENTA}${BOLD}================================================${NC}"
    echo -e " ${YELLOW}基于:${NC}      ${GREEN}lvhy.sh (${ORIGINAL_AUTHOR})${NC}"
    echo -e " ${YELLOW}增强:${NC}      ${GREEN}${AUTHOR_NAME}${NC}"
    echo -e " ${YELLOW}功能:${NC}      ${GREEN}Trojan 协议添加和配置管理${NC}"
    echo -e "${MAGENTA}${BOLD}================================================${NC}"
}

show_menu() {
    echo
    echo -e "${GREEN}${BOLD}请选择操作:${NC}"
    echo -e "${CYAN}  1.${NC} 添加/重新配置 Trojan 协议"
    echo -e "${CYAN}  2.${NC} 查看当前 Trojan 配置信息"
    echo -e "${CYAN}  0.${NC} 退出"
    echo -e "${MAGENTA}${BOLD}================================================${NC}"
}

load_trojan_info() {
    if [ -f "$PERSISTENT_INFO_FILE" ]; then
        info "加载 Trojan 配置信息从: $PERSISTENT_INFO_FILE"
        # Source 文件以加载变量
        source "$PERSISTENT_INFO_FILE"
        success "Trojan 配置信息加载完成"
    else
        info "未找到持久化的 Trojan 配置信息文件"
    fi
}

show_trojan_info() {
    # 加载持久化信息
    load_trojan_info

    # 检查是否有 Trojan 配置信息
    if [ -z "$LAST_TROJAN_PORT" ] || [ -z "$LAST_TROJAN_PASSWORD" ]; then
        warn "未找到 Trojan 配置信息"
        info "请先运行选项 1 添加 Trojan 配置，或确保 ${PERSISTENT_INFO_FILE} 文件存在且包含 Trojan 信息"
        return
    fi

    info "显示当前 Trojan 配置信息:"

    # 重新生成链接（带时间戳）
    local trojan_link="trojan://${LAST_TROJAN_PASSWORD}@${LAST_SERVER_IP}:${LAST_TROJAN_PORT}?sni=${LAST_TROJAN_DOMAIN}&allowInsecure=1#Trojan-${LAST_SERVER_IP}-$(date +%s)"

    echo
    echo -e "${GREEN}${BOLD}=== Trojan 配置信息 ===${NC}"
    echo -e "${CYAN}服务器地址:${NC} ${GREEN}${LAST_SERVER_IP}${NC}"
    echo -e "${CYAN}端口:${NC} ${GREEN}${LAST_TROJAN_PORT}${NC}"
    echo -e "${CYAN}密码:${NC} ${GREEN}${LAST_TROJAN_PASSWORD}${NC}"
    echo -e "${CYAN}SNI/域名:${NC} ${GREEN}${LAST_TROJAN_DOMAIN}${NC}"
    echo -e "${CYAN}传输协议:${NC} ${GREEN}TCP${NC}"
    echo -e "${CYAN}TLS:${NC} ${GREEN}启用 (自签名证书)${NC}"
    echo
    echo -e "${CYAN}Trojan 导入链接:${NC} ${GREEN}${trojan_link}${NC}"
    echo

    # 生成二维码（如果可用）
    if command -v qrencode >/dev/null 2>&1; then
        echo -e "${CYAN}Trojan 二维码:${NC}"
        qrencode -t ansiutf8 "$trojan_link"
        echo
    else
        info "安装 qrencode 以显示二维码: apt install qrencode 或 yum install qrencode"
    fi

    echo -e "${MAGENTA}${BOLD}================================================${NC}"
}

check_root() {
    if [ "$EUID" -ne 0 ]; then
        error "请使用 root 权限运行此脚本"
        exit 1
    fi
}

find_singbox_cmd() {
    if [ -x "$SINGBOX_INSTALL_PATH_EXPECTED" ]; then
        SINGBOX_CMD="$SINGBOX_INSTALL_PATH_EXPECTED"
        info "找到 Sing-Box: $SINGBOX_CMD"
    else
        error "未找到 Sing-Box 安装。请先运行 lvhy.sh 安装 Sing-Box"
        exit 1
    fi
}

check_existing_config() {
    if [ ! -f "$SINGBOX_CONFIG_FILE" ]; then
        error "未找到现有的 Sing-Box 配置文件: $SINGBOX_CONFIG_FILE"
        error "请先运行 lvhy.sh 安装 Hysteria2 和 Reality"
        exit 1
    fi
    
    info "检测到现有配置文件: $SINGBOX_CONFIG_FILE"
    
    # 检查是否已经包含 Trojan
    if grep -q '"type": "trojan"' "$SINGBOX_CONFIG_FILE"; then
        warn "配置文件中已存在 Trojan 协议配置"
        read -p "是否要重新配置 Trojan? (y/N): " reconfigure
        if [[ ! "$reconfigure" =~ ^[Yy]$ ]]; then
            info "取消操作"
            exit 0
        fi
    fi
}

generate_trojan_password() {
    TROJAN_PASSWORD=$(openssl rand -hex 16)
    info "生成的 Trojan 密码: $TROJAN_PASSWORD"
}

get_server_ip() {
    local server_ip
    server_ip=$(curl -s --max-time 10 ipv4.icanhazip.com)
    if [ -z "$server_ip" ]; then
        server_ip=$(curl -s --max-time 10 ipinfo.io/ip)
    fi
    if [ -z "$server_ip" ]; then
        server_ip=$(curl -s --max-time 10 ifconfig.me)
    fi
    if [ -z "$server_ip" ]; then
        warn "无法自动获取服务器IP，请手动输入"
        read -p "请输入服务器IP: " server_ip
    fi
    echo "$server_ip"
}

generate_trojan_cert() {
    local domain="$1"
    info "正在为 Trojan 生成自签名证书 (CN=${domain})..."
    
    mkdir -p "$TROJAN_CERT_DIR"
    
    # 生成私钥 (2048位RSA)
    if ! openssl genrsa -out "$TROJAN_CERT_KEY" 2048; then
        error "生成私钥失败"
        return 1
    fi
    
    # 生成自签名证书
    if ! openssl req -new -x509 -key "$TROJAN_CERT_KEY" -out "$TROJAN_CERT_PEM" -days 365 -subj "/CN=${domain}"; then
        error "生成证书失败"
        return 1
    fi
    
    # 验证文件是否成功创建
    if [ ! -f "$TROJAN_CERT_KEY" ] || [ ! -f "$TROJAN_CERT_PEM" ]; then
        error "证书文件创建失败"
        return 1
    fi
    
    # 设置权限
    chmod 600 "$TROJAN_CERT_KEY"
    chmod 644 "$TROJAN_CERT_PEM"
    
    # 验证证书内容
    if ! openssl x509 -in "$TROJAN_CERT_PEM" -text -noout >/dev/null 2>&1; then
        error "生成的证书文件无效"
        return 1
    fi
    
    success "Trojan 证书生成完成"
    info "证书路径: $TROJAN_CERT_PEM"
    info "私钥路径: $TROJAN_CERT_KEY"
    
    # 显示证书信息用于调试
    info "证书详情:"
    openssl x509 -in "$TROJAN_CERT_PEM" -subject -dates -noout
}

backup_config() {
    local backup_file="${SINGBOX_CONFIG_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$SINGBOX_CONFIG_FILE" "$backup_file"
    info "配置文件已备份到: $backup_file"
}

add_trojan_to_config() {
    local trojan_port="$1"
    local trojan_password="$2"
    local trojan_domain="$3"
    
    info "正在添加 Trojan 配置到现有配置文件..."
    
    # 备份原配置
    backup_config
    
    # 验证证书文件存在
    if [ ! -f "$TROJAN_CERT_PEM" ] || [ ! -f "$TROJAN_CERT_KEY" ]; then
        error "Trojan 证书文件不存在，请重新生成证书"
        return 1
    fi
    
    # 创建临时配置文件
    local temp_config="/tmp/singbox_config_temp.json"
    
    # 使用 jq 添加 Trojan inbound 到现有配置
    if command -v jq >/dev/null 2>&1; then
        info "使用 jq 合并配置..."

        # 首先删除现有的 Trojan 配置（如果存在）
        info "删除现有的 Trojan 配置..."
        if jq '.inbounds |= map(select(.tag != "trojan-in"))' "$SINGBOX_CONFIG_FILE" > "$temp_config"; then
            mv "$temp_config" "$SINGBOX_CONFIG_FILE"
            info "已删除现有的 Trojan 配置"
        else
            error "删除现有 Trojan 配置失败"
            rm -f "$temp_config"
            return 1
        fi

        # 创建 Trojan inbound 配置
        local trojan_config=$(cat <<EOF
{
    "type": "trojan",
    "tag": "trojan-in",
    "listen": "::",
    "listen_port": ${trojan_port},
    "users": [
        {
            "password": "${trojan_password}"
        }
    ],
    "tls": {
        "enabled": true,
        "server_name": "${trojan_domain}",
        "certificate_path": "${TROJAN_CERT_PEM}",
        "key_path": "${TROJAN_CERT_KEY}"
    },
    "fallback": {
        "server": "${trojan_domain}",
        "server_port": 443
    }
}
EOF
)

        # 使用 jq 合并配置
        if jq --argjson trojan "$trojan_config" '.inbounds += [$trojan]' "$SINGBOX_CONFIG_FILE" > "$temp_config"; then
            mv "$temp_config" "$SINGBOX_CONFIG_FILE"
            success "使用 jq 成功合并配置"
        else
            error "jq 合并配置失败"
            rm -f "$temp_config"
            return 1
        fi
    else
        # 如果没有 jq，使用 Python 处理 JSON
        info "未找到 jq，尝试使用 Python 处理 JSON..."
        
        python3 -c "
import json
import sys

# 读取现有配置
with open('$SINGBOX_CONFIG_FILE', 'r') as f:
    config = json.load(f)

# 删除现有的 Trojan 配置
config['inbounds'] = [inbound for inbound in config['inbounds'] if inbound.get('tag') != 'trojan-in']

# 创建 Trojan inbound
trojan_inbound = {
    'type': 'trojan',
    'tag': 'trojan-in',
    'listen': '::',
    'listen_port': ${trojan_port},
    'users': [{
        'password': '${trojan_password}'
    }],
    'tls': {
        'enabled': True,
        'server_name': '${trojan_domain}',
        'certificate_path': '${TROJAN_CERT_PEM}',
        'key_path': '${TROJAN_CERT_KEY}'
    },
    'fallback': {
        'server': '${trojan_domain}',
        'server_port': 443
    }
}

# 添加到 inbounds
config['inbounds'].append(trojan_inbound)

# 写入文件
with open('$temp_config', 'w') as f:
    json.dump(config, f, indent=2)
" 2>/dev/null
        
        if [ $? -eq 0 ] && [ -f "$temp_config" ]; then
            mv "$temp_config" "$SINGBOX_CONFIG_FILE"
            success "使用 Python 成功合并配置"
        else
            error "Python JSON 处理失败，使用手动文本处理..."
            rm -f "$temp_config"
            
            # 手动文本处理作为最后手段
            manual_add_trojan_config "$trojan_port" "$trojan_password" "$trojan_domain"
        fi
    fi
    
    success "Trojan 配置已添加到 Sing-Box 配置文件"
}

# 手动文本处理函数
manual_add_trojan_config() {
    local trojan_port="$1"
    local trojan_password="$2"
    local trojan_domain="$3"
    
    info "使用手动文本处理添加 Trojan 配置..."
    
    # 创建 Trojan 配置块
    local trojan_block="        ,
        {
            \"type\": \"trojan\",
            \"tag\": \"trojan-in\",
            \"listen\": \"::\",
            \"listen_port\": ${trojan_port},
            \"users\": [
                {
                    \"password\": \"${trojan_password}\"
                }
            ],
            \"tls\": {
                \"enabled\": true,
                \"server_name\": \"${trojan_domain}\",
                \"certificate_path\": \"${TROJAN_CERT_PEM}\",
                \"key_path\": \"${TROJAN_CERT_KEY}\"
            },
            \"fallback\": {
                \"server\": \"${trojan_domain}\",
                \"server_port\": 443
            }
        }"
    
    # 在 inbounds 数组的最后一个元素后添加配置
    # 查找 inbounds 数组结束的位置
    local line_num=$(grep -n '"inbounds"' "$SINGBOX_CONFIG_FILE" | head -1 | cut -d: -f1)
    local end_line=$(tail -n +$((line_num+1)) "$SINGBOX_CONFIG_FILE" | grep -n '^    ]' | head -1 | cut -d: -f1)
    end_line=$((line_num + end_line))
    
    # 在结束括号前插入 Trojan 配置
    sed -i "${end_line}i\\$trojan_block" "$SINGBOX_CONFIG_FILE"
    
    success "手动文本处理完成"
}

validate_config() {
    info "正在验证配置文件..."
    if $SINGBOX_CMD check -c "$SINGBOX_CONFIG_FILE"; then
        success "配置文件验证通过"
        
        info "正在格式化配置文件..."
        if $SINGBOX_CMD format -c "$SINGBOX_CONFIG_FILE" -w; then
            success "配置文件格式化完成"
        else
            warn "配置文件格式化失败，但语法正确"
        fi
    else
        error "配置文件验证失败"
        error "请检查配置文件: $SINGBOX_CONFIG_FILE"
        return 1
    fi
}

restart_singbox_service() {
    info "正在重启 Sing-Box 服务..."
    systemctl restart sing-box
    sleep 2
    
    if systemctl is-active --quiet sing-box; then
        success "Sing-Box 服务重启成功"
    else
        error "Sing-Box 服务重启失败"
        error "请检查服务状态: systemctl status sing-box"
        return 1
    fi
}

generate_trojan_link() {
    local server_ip="$1"
    local trojan_port="$2"
    local trojan_password="$3"
    local trojan_domain="$4"
    
    # Trojan URI format: trojan://password@server:port?sni=domain#tag
    local trojan_link="trojan://${trojan_password}@${server_ip}:${trojan_port}?sni=${trojan_domain}&allowInsecure=1#Trojan-${server_ip}"
    echo "$trojan_link"
}

display_trojan_info() {
    local server_ip="$1"
    local trojan_port="$2"
    local trojan_password="$3"
    local trojan_domain="$4"
    local trojan_link="$5"
    
    echo
    echo -e "${GREEN}${BOLD}=== Trojan 配置信息 ===${NC}"
    echo -e "${CYAN}服务器地址:${NC} ${GREEN}${server_ip}${NC}"
    echo -e "${CYAN}端口:${NC} ${GREEN}${trojan_port}${NC}"
    echo -e "${CYAN}密码:${NC} ${GREEN}${trojan_password}${NC}"
    echo -e "${CYAN}SNI/域名:${NC} ${GREEN}${trojan_domain}${NC}"
    echo -e "${CYAN}传输协议:${NC} ${GREEN}TCP${NC}"
    echo -e "${CYAN}TLS:${NC} ${GREEN}启用 (自签名证书)${NC}"
    echo
    echo -e "${CYAN}Trojan 导入链接:${NC} ${GREEN}${trojan_link}${NC}"
    echo
    
    # 生成二维码（如果可用）
    if command -v qrencode >/dev/null 2>&1; then
        echo -e "${CYAN}Trojan 二维码:${NC}"
        qrencode -t ansiutf8 "$trojan_link"
        echo
    else
        info "安装 qrencode 以显示二维码: apt install qrencode 或 yum install qrencode"
    fi
}

save_trojan_info() {
    local server_ip="$1"
    local trojan_port="$2"
    local trojan_password="$3"
    local trojan_domain="$4"
    local trojan_link="$5"
    
    # 添加到持久化信息文件
    if [ -f "$PERSISTENT_INFO_FILE" ]; then
        # 添加 Trojan 信息到现有文件
        cat >> "$PERSISTENT_INFO_FILE" <<EOF
LAST_TROJAN_PORT="${trojan_port}"
LAST_TROJAN_PASSWORD="${trojan_password}"
LAST_TROJAN_DOMAIN="${trojan_domain}"
LAST_TROJAN_LINK="${trojan_link}"
EOF
    else
        warn "未找到持久化信息文件，创建新文件"
        mkdir -p "$(dirname "$PERSISTENT_INFO_FILE")"
        cat > "$PERSISTENT_INFO_FILE" <<EOF
LAST_SERVER_IP="${server_ip}"
LAST_TROJAN_PORT="${trojan_port}"
LAST_TROJAN_PASSWORD="${trojan_password}"
LAST_TROJAN_DOMAIN="${trojan_domain}"
LAST_TROJAN_LINK="${trojan_link}"
EOF
    fi
    
    success "Trojan 配置信息已保存"
}

add_trojan_config() {
    # 检查现有配置
    check_existing_config

    # 获取用户输入
    echo
    info "开始配置 Trojan 协议..."

    read -p "请输入 Trojan 监听端口 (默认: ${DEFAULT_TROJAN_PORT}): " input_port
    TROJAN_PORT=${input_port:-$DEFAULT_TROJAN_PORT}

    read -p "请输入 Trojan SNI/域名 (默认: ${DEFAULT_TROJAN_DOMAIN}): " input_domain
    TROJAN_DOMAIN=${input_domain:-$DEFAULT_TROJAN_DOMAIN}

    # 生成密码
    generate_trojan_password

    # 获取服务器IP
    SERVER_IP=$(get_server_ip)
    info "检测到服务器IP: $SERVER_IP"

    # 生成证书
    generate_trojan_cert "$TROJAN_DOMAIN"

    # 添加配置
    add_trojan_to_config "$TROJAN_PORT" "$TROJAN_PASSWORD" "$TROJAN_DOMAIN"

    # 验证配置
    if ! validate_config; then
        error "配置验证失败，请检查配置"
        return 1
    fi

    # 重启服务
    if ! restart_singbox_service; then
        error "服务重启失败，请手动检查"
        return 1
    fi

    # 生成连接信息
    TROJAN_LINK=$(generate_trojan_link "$SERVER_IP" "$TROJAN_PORT" "$TROJAN_PASSWORD" "$TROJAN_DOMAIN")

    # 显示配置信息
    display_trojan_info "$SERVER_IP" "$TROJAN_PORT" "$TROJAN_PASSWORD" "$TROJAN_DOMAIN" "$TROJAN_LINK"

    # 保存配置信息
    save_trojan_info "$SERVER_IP" "$TROJAN_PORT" "$TROJAN_PASSWORD" "$TROJAN_DOMAIN" "$TROJAN_LINK"

    echo
    success "Trojan 协议添加完成！"
    info "现在您的服务器同时支持 Hysteria2、Reality 和 Trojan 三种协议"
    info "可以使用 'systemctl status sing-box' 检查服务状态"
    info "可以使用 'journalctl -u sing-box -f' 查看实时日志"
    echo
}

main() {
    print_header

    # 检查权限
    check_root

    # 查找 Sing-Box
    find_singbox_cmd

    # 显示菜单并处理用户选择
    show_menu
    echo -n "请输入选项 (0-2): "
    read choice

    case $choice in
        1)
            add_trojan_config
            ;;
        2)
            show_trojan_info
            ;;
        0)
            info "退出脚本"
            exit 0
            ;;
        *)
            error "无效选项，请输入 0-2 之间的数字"
            exit 1
            ;;
    esac
}

# 脚本入口
main "$@"
