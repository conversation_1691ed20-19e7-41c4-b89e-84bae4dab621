#!/bin/bash

# SOCKS5代理转发配置脚本 - 通用版本
# 自动检测sing-box配置并仅对Reality (VLESS)协议传入的流量进行SOCKS5代理转发
# 作者: AI Assistant
# 版本: 2.0 (Universal)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局变量 - 将在运行时自动检测
SINGBOX_CONFIG=""
BACKUP_DIR=""
SCRIPT_LOG="/var/log/socks5_proxy_setup.log"
VLESS_INBOUND_TAG=""
VLESS_PORT=""

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$SCRIPT_LOG"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$SCRIPT_LOG"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$SCRIPT_LOG"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$SCRIPT_LOG"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "此脚本需要root权限运行"
        exit 1
    fi
}

# 自动检测sing-box配置文件路径
detect_singbox_config() {
    info "正在检测sing-box配置..."
    
    # 常见的配置文件路径
    local possible_paths=(
        "/usr/local/etc/sing-box/config.json"
        "/etc/sing-box/config.json"
        "/opt/sing-box/config.json"
        "/root/sing-box/config.json"
        "/home/<USER>/config.json"
    )
    
    # 首先尝试从systemd服务文件中获取配置路径
    if systemctl is-enabled sing-box &>/dev/null; then
        local service_config
        service_config=$(systemctl cat sing-box 2>/dev/null | grep -o '\-c [^[:space:]]*' | cut -d' ' -f2 | head -1)
        if [[ -n "$service_config" ]] && [[ -f "$service_config" ]]; then
            SINGBOX_CONFIG="$service_config"
            info "从systemd服务检测到配置文件: $SINGBOX_CONFIG"
            return 0
        fi
    fi
    
    # 尝试常见路径
    for path in "${possible_paths[@]}"; do
        if [[ -f "$path" ]]; then
            SINGBOX_CONFIG="$path"
            info "检测到配置文件: $SINGBOX_CONFIG"
            return 0
        fi
    done
    
    # 尝试查找配置文件
    local found_configs
    found_configs=$(find /etc /usr/local /opt /root -name "config.json" -path "*/sing-box/*" 2>/dev/null | head -5)
    
    if [[ -n "$found_configs" ]]; then
        echo -e "\n${YELLOW}找到以下可能的sing-box配置文件:${NC}"
        echo "$found_configs" | nl
        echo
        read -p "请选择配置文件编号 (1-$(echo "$found_configs" | wc -l)): " choice
        
        if [[ "$choice" =~ ^[0-9]+$ ]]; then
            SINGBOX_CONFIG=$(echo "$found_configs" | sed -n "${choice}p")
            if [[ -f "$SINGBOX_CONFIG" ]]; then
                info "用户选择的配置文件: $SINGBOX_CONFIG"
                return 0
            fi
        fi
    fi
    
    error "无法找到sing-box配置文件"
    echo "请确保sing-box已正确安装并配置"
    return 1
}

# 分析sing-box配置，检测VLESS入站
analyze_singbox_config() {
    info "分析sing-box配置..."
    
    if [[ ! -f "$SINGBOX_CONFIG" ]]; then
        error "配置文件不存在: $SINGBOX_CONFIG"
        return 1
    fi
    
    # 使用Python分析配置
    local analysis_result
    analysis_result=$(python3 << EOF
import json
import sys

try:
    with open('$SINGBOX_CONFIG', 'r') as f:
        config = json.load(f)
    
    vless_inbounds = []
    
    # 查找VLESS入站配置
    for inbound in config.get('inbounds', []):
        if inbound.get('type') == 'vless':
            tag = inbound.get('tag', 'unnamed')
            port = inbound.get('listen_port', 'unknown')
            
            # 检查是否有Reality配置
            tls_config = inbound.get('tls', {})
            has_reality = tls_config.get('reality', {}).get('enabled', False)
            
            vless_inbounds.append({
                'tag': tag,
                'port': port,
                'has_reality': has_reality
            })
    
    if not vless_inbounds:
        print("ERROR:未找到VLESS入站配置")
        sys.exit(1)
    
    # 优先选择有Reality的VLESS入站
    reality_inbounds = [ib for ib in vless_inbounds if ib['has_reality']]
    
    if reality_inbounds:
        selected = reality_inbounds[0]
        print(f"VLESS_TAG:{selected['tag']}")
        print(f"VLESS_PORT:{selected['port']}")
        print(f"HAS_REALITY:true")
    else:
        selected = vless_inbounds[0]
        print(f"VLESS_TAG:{selected['tag']}")
        print(f"VLESS_PORT:{selected['port']}")
        print(f"HAS_REALITY:false")
        print("WARNING:未检测到Reality配置，但找到VLESS入站")
    
    if len(vless_inbounds) > 1:
        print("INFO:检测到多个VLESS入站配置")
        for i, ib in enumerate(vless_inbounds):
            reality_status = "有Reality" if ib['has_reality'] else "无Reality"
            print(f"INFO:  {i+1}. 标签: {ib['tag']}, 端口: {ib['port']}, {reality_status}")

except Exception as e:
    print(f"ERROR:配置分析失败: {e}")
    sys.exit(1)
EOF
)
    
    if [[ $? -ne 0 ]]; then
        error "配置分析失败"
        return 1
    fi
    
    # 解析分析结果
    while IFS= read -r line; do
        if [[ "$line" =~ ^VLESS_TAG: ]]; then
            VLESS_INBOUND_TAG="${line#VLESS_TAG:}"
        elif [[ "$line" =~ ^VLESS_PORT: ]]; then
            VLESS_PORT="${line#VLESS_PORT:}"
        elif [[ "$line" =~ ^HAS_REALITY: ]]; then
            local has_reality="${line#HAS_REALITY:}"
            if [[ "$has_reality" == "false" ]]; then
                warning "检测到VLESS配置但没有Reality，脚本仍可使用但建议配置Reality"
            fi
        elif [[ "$line" =~ ^WARNING: ]]; then
            warning "${line#WARNING:}"
        elif [[ "$line" =~ ^INFO: ]]; then
            info "${line#INFO:}"
        elif [[ "$line" =~ ^ERROR: ]]; then
            error "${line#ERROR:}"
            return 1
        fi
    done <<< "$analysis_result"
    
    if [[ -z "$VLESS_INBOUND_TAG" ]]; then
        error "无法确定VLESS入站标签"
        return 1
    fi
    
    info "检测到VLESS入站 - 标签: $VLESS_INBOUND_TAG, 端口: $VLESS_PORT"
    
    # 设置备份目录
    BACKUP_DIR="$(dirname "$SINGBOX_CONFIG")/backups"
    
    return 0
}

# 检查sing-box是否存在并分析配置
check_singbox() {
    if ! command -v sing-box &> /dev/null; then
        error "sing-box未安装或不在PATH中"
        exit 1
    fi
    
    if ! detect_singbox_config; then
        exit 1
    fi
    
    if ! analyze_singbox_config; then
        exit 1
    fi
    
    if ! systemctl is-active --quiet sing-box; then
        warning "sing-box服务未运行"
    fi
}

# 显示检测到的配置信息
show_detected_config() {
    echo -e "\n${BLUE}=== 检测到的配置信息 ===${NC}"
    echo "配置文件: $SINGBOX_CONFIG"
    echo "VLESS入站标签: $VLESS_INBOUND_TAG"
    echo "监听端口: $VLESS_PORT"
    echo "备份目录: $BACKUP_DIR"
    echo
}

# 创建备份目录
create_backup_dir() {
    if [[ ! -d "$BACKUP_DIR" ]]; then
        mkdir -p "$BACKUP_DIR"
        log "创建备份目录: $BACKUP_DIR"
    fi
}

# 备份当前配置
backup_config() {
    local backup_file="$BACKUP_DIR/config.json.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$SINGBOX_CONFIG" "$backup_file"
    log "配置已备份到: $backup_file"
    echo "$backup_file" > "$BACKUP_DIR/.last_backup"
}

# 收集SOCKS5代理信息
collect_proxy_info() {
    echo -e "\n${BLUE}=== SOCKS5代理配置 ===${NC}"
    echo "请输入您购买的SOCKS5住宅代理服务信息："
    echo

    # 代理服务器地址
    while true; do
        read -p "代理服务器地址 (IP或域名): " PROXY_HOST
        if [[ -n "$PROXY_HOST" ]]; then
            break
        else
            error "代理服务器地址不能为空"
        fi
    done

    # 代理端口
    while true; do
        read -p "代理端口: " PROXY_PORT
        if [[ "$PROXY_PORT" =~ ^[0-9]+$ ]] && [[ "$PROXY_PORT" -ge 1 ]] && [[ "$PROXY_PORT" -le 65535 ]]; then
            break
        else
            error "请输入有效的端口号 (1-65535)"
        fi
    done

    # 用户名
    read -p "用户名: " PROXY_USER

    # 密码
    echo -n "密码: "
    read -s PROXY_PASS
    echo

    # 确认信息
    echo -e "\n${YELLOW}请确认代理信息:${NC}"
    echo "服务器: $PROXY_HOST"
    echo "端口: $PROXY_PORT"
    echo "用户名: $PROXY_USER"
    echo "密码: $(echo "$PROXY_PASS" | sed 's/./*/g')"
    echo

    while true; do
        read -p "信息是否正确? (y/n): " confirm
        case $confirm in
            [Yy]* ) break;;
            [Nn]* )
                echo "请重新输入信息"
                collect_proxy_info
                return
                ;;
            * ) echo "请输入 y 或 n";;
        esac
    done
}

# 验证JSON格式
validate_json() {
    local json_file="$1"
    if ! python3 -m json.tool "$json_file" > /dev/null 2>&1; then
        if ! jq . "$json_file" > /dev/null 2>&1; then
            return 1
        fi
    fi
    return 0
}

# 修改sing-box配置 - 使用检测到的入站标签
modify_singbox_config() {
    log "开始修改sing-box配置..."

    # 创建临时配置文件
    local temp_config="/tmp/singbox_config_temp.json"
    cp "$SINGBOX_CONFIG" "$temp_config"

    # 使用Python脚本修改JSON配置
    python3 << EOF
import json
import sys

try:
    # 读取配置文件
    with open('$temp_config', 'r') as f:
        config = json.load(f)

    # 添加SOCKS5出站配置
    socks5_outbound = {
        "type": "socks",
        "tag": "socks5-proxy",
        "server": "$PROXY_HOST",
        "server_port": $PROXY_PORT,
        "username": "$PROXY_USER",
        "password": "$PROXY_PASS",
        "version": "5"
    }

    # 确保outbounds数组存在
    if 'outbounds' not in config:
        config['outbounds'] = []

    # 检查是否已存在socks5-proxy配置
    existing_socks5 = False
    for i, outbound in enumerate(config['outbounds']):
        if outbound.get('tag') == 'socks5-proxy':
            config['outbounds'][i] = socks5_outbound
            existing_socks5 = True
            break

    if not existing_socks5:
        config['outbounds'].insert(0, socks5_outbound)

    # 修改路由规则 - 使用检测到的入站标签
    if 'route' not in config:
        config['route'] = {}
    if 'rules' not in config['route']:
        config['route']['rules'] = []

    # 添加VLESS流量转发到SOCKS5的规则
    vless_rule = {
        "inbound": ["$VLESS_INBOUND_TAG"],
        "outbound": "socks5-proxy"
    }

    # 检查是否已存在相同规则
    rule_exists = False
    for i, rule in enumerate(config['route']['rules']):
        if rule.get('inbound') == ["$VLESS_INBOUND_TAG"]:
            config['route']['rules'][i] = vless_rule
            rule_exists = True
            break

    if not rule_exists:
        config['route']['rules'].insert(0, vless_rule)

    # 写入配置文件
    with open('$temp_config', 'w') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)

    print("配置修改成功")

except Exception as e:
    print(f"配置修改失败: {e}")
    sys.exit(1)
EOF

    if [[ $? -eq 0 ]]; then
        # 验证修改后的配置
        if validate_json "$temp_config"; then
            # 应用新配置
            cp "$temp_config" "$SINGBOX_CONFIG"
            rm "$temp_config"
            log "sing-box配置修改成功"
        else
            error "修改后的配置JSON格式无效"
            rm "$temp_config"
            exit 1
        fi
    else
        error "配置修改失败"
        rm -f "$temp_config"
        exit 1
    fi
}

# 重启sing-box服务
restart_singbox() {
    log "重启sing-box服务..."

    # 验证配置文件
    if ! sing-box check -c "$SINGBOX_CONFIG"; then
        error "sing-box配置文件验证失败"
        return 1
    fi

    # 重启服务
    if systemctl restart sing-box; then
        sleep 3
        if systemctl is-active --quiet sing-box; then
            log "sing-box服务重启成功"
            return 0
        else
            error "sing-box服务启动失败"
            return 1
        fi
    else
        error "sing-box服务重启失败"
        return 1
    fi
}

# 测试代理连接
test_proxy_connection() {
    log "测试SOCKS5代理连接..."

    # 使用curl测试代理连接
    if command -v curl &> /dev/null; then
        local test_url="http://httpbin.org/ip"
        local timeout=10

        info "测试代理连接到: $test_url"

        # 直连测试
        local direct_ip
        direct_ip=$(curl -s --max-time $timeout "$test_url" | grep -o '"origin": "[^"]*"' | cut -d'"' -f4 2>/dev/null || echo "获取失败")
        info "直连IP: $direct_ip"

        # 代理测试
        local proxy_ip
        proxy_ip=$(curl -s --max-time $timeout --socks5-hostname "$PROXY_HOST:$PROXY_PORT" \
                   --proxy-user "$PROXY_USER:$PROXY_PASS" "$test_url" | \
                   grep -o '"origin": "[^"]*"' | cut -d'"' -f4 2>/dev/null || echo "连接失败")

        if [[ "$proxy_ip" != "连接失败" ]] && [[ "$proxy_ip" != "$direct_ip" ]]; then
            log "SOCKS5代理连接测试成功，代理IP: $proxy_ip"
            return 0
        else
            warning "SOCKS5代理连接测试失败或IP未改变"
            return 1
        fi
    else
        warning "curl未安装，跳过代理连接测试"
        return 0
    fi
}

# 回滚配置
rollback_config() {
    local backup_file
    if [[ -f "$BACKUP_DIR/.last_backup" ]]; then
        backup_file=$(cat "$BACKUP_DIR/.last_backup")
        if [[ -f "$backup_file" ]]; then
            log "回滚到备份配置: $backup_file"
            cp "$backup_file" "$SINGBOX_CONFIG"

            if restart_singbox; then
                log "配置回滚成功"
                return 0
            else
                error "配置回滚后服务启动失败"
                return 1
            fi
        else
            error "备份文件不存在: $backup_file"
            return 1
        fi
    else
        error "未找到备份文件记录"
        return 1
    fi
}

# 显示当前配置状态
show_status() {
    echo -e "\n${BLUE}=== 当前配置状态 ===${NC}"

    # 如果还没有检测配置，先检测
    if [[ -z "$SINGBOX_CONFIG" ]]; then
        if ! detect_singbox_config || ! analyze_singbox_config; then
            error "无法检测sing-box配置"
            return 1
        fi
    fi

    echo "配置文件: $SINGBOX_CONFIG"
    echo "VLESS入站: $VLESS_INBOUND_TAG (端口: $VLESS_PORT)"

    # sing-box服务状态
    if systemctl is-active --quiet sing-box; then
        echo -e "sing-box服务: ${GREEN}运行中${NC}"
    else
        echo -e "sing-box服务: ${RED}未运行${NC}"
    fi

    # 检查配置中是否有SOCKS5代理
    if grep -q "socks5-proxy" "$SINGBOX_CONFIG" 2>/dev/null; then
        echo -e "SOCKS5代理: ${GREEN}已配置${NC}"

        # 提取代理信息
        local proxy_info
        proxy_info=$(python3 << EOF
import json
try:
    with open('$SINGBOX_CONFIG', 'r') as f:
        config = json.load(f)

    for outbound in config.get('outbounds', []):
        if outbound.get('tag') == 'socks5-proxy':
            print(f"  服务器: {outbound.get('server', 'N/A')}")
            print(f"  端口: {outbound.get('server_port', 'N/A')}")
            print(f"  用户名: {outbound.get('username', 'N/A')}")
            break
except:
    print("  无法读取代理配置")
EOF
)
        echo "$proxy_info"
    else
        echo -e "SOCKS5代理: ${YELLOW}未配置${NC}"
    fi

    # 监听端口
    local listening_ports
    listening_ports=$(netstat -tlnp 2>/dev/null | grep sing-box | awk '{print $4}' | sed 's/.*://' | sort -n | tr '\n' ' ')
    echo "监听端口: ${listening_ports:-无}"

    echo
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}SOCKS5代理转发配置脚本 - 通用版本${NC}"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  setup     - 交互式配置SOCKS5代理转发"
    echo "  status    - 显示当前配置状态"
    echo "  test      - 测试代理连接"
    echo "  rollback  - 回滚到上一个配置"
    echo "  detect    - 仅检测并显示sing-box配置信息"
    echo "  help      - 显示此帮助信息"
    echo
    echo "功能说明:"
    echo "  此脚本会自动检测sing-box配置，仅对检测到的VLESS入站流量进行SOCKS5代理转发"
    echo "  其他协议的流量将保持直连，不受影响"
    echo
    echo "兼容性:"
    echo "  - 自动检测sing-box配置文件路径"
    echo "  - 自动检测VLESS入站配置和标签"
    echo "  - 支持Reality和非Reality的VLESS配置"
    echo "  - 适用于不同的sing-box安装方式"
    echo
}

# 主函数
main() {
    # 创建日志文件
    touch "$SCRIPT_LOG"

    case "${1:-setup}" in
        "setup")
            log "开始SOCKS5代理配置..."
            check_root
            check_singbox
            show_detected_config

            # 确认配置
            echo -e "${YELLOW}检测到的配置信息如上所示。${NC}"
            read -p "是否继续配置SOCKS5代理? (y/n): " confirm
            case $confirm in
                [Yy]* ) ;;
                * )
                    echo "配置已取消"
                    exit 0
                    ;;
            esac

            create_backup_dir
            backup_config
            collect_proxy_info
            modify_singbox_config

            if restart_singbox; then
                log "配置完成！"
                show_status

                echo -e "\n${GREEN}配置成功完成！${NC}"
                echo "现在通过VLESS入站($VLESS_INBOUND_TAG)连接的流量将通过SOCKS5代理转发"
                echo "其他协议的流量保持直连"
                echo
                echo "建议运行测试: $0 test"
            else
                error "服务重启失败，正在回滚配置..."
                rollback_config
                exit 1
            fi
            ;;
        "status")
            show_status
            ;;
        "detect")
            if detect_singbox_config && analyze_singbox_config; then
                show_detected_config
            else
                exit 1
            fi
            ;;
        "test")
            # 先检测配置
            if [[ -z "$SINGBOX_CONFIG" ]]; then
                if ! detect_singbox_config || ! analyze_singbox_config; then
                    error "无法检测sing-box配置"
                    exit 1
                fi
            fi

            if [[ -z "$PROXY_HOST" ]]; then
                # 从配置文件读取代理信息进行测试
                eval $(python3 << EOF
import json
try:
    with open('$SINGBOX_CONFIG', 'r') as f:
        config = json.load(f)

    for outbound in config.get('outbounds', []):
        if outbound.get('tag') == 'socks5-proxy':
            print(f"PROXY_HOST='{outbound.get('server', '')}'")
            print(f"PROXY_PORT='{outbound.get('server_port', '')}'")
            print(f"PROXY_USER='{outbound.get('username', '')}'")
            print(f"PROXY_PASS='{outbound.get('password', '')}'")
            break
except:
    pass
EOF
)
            fi
            test_proxy_connection
            ;;
        "rollback")
            check_root
            if [[ -z "$SINGBOX_CONFIG" ]]; then
                detect_singbox_config
            fi
            rollback_config
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
